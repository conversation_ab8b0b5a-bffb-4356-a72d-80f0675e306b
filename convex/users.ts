import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createUser = mutation({
  args: {
    userId: v.string(),
    email: v.string(),
    name: v.string(),
    createdAt: v.number(),
    profileImage: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const newUser = await ctx.db.insert("users", {
        userId: args.userId,
        email: args.email,
        name: args.name,
        createdAt: args.createdAt,
        profileImg: args.profileImage,
      });

      return newUser;
    } catch (error) {
      throw new Error("User information did not insert successfully");
    }
  },
});

export const readUser = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userInfo = await ctx.db
        .query("users")
        .filter((user) => user.eq(user.field("userId"), args.userId))
        .first();

      return userInfo;
    } catch (error) {
      throw new Error("Reading user did not work");
    }
  },
});

export const update = mutation({
  args: {
    userId: v.optional(v.string()),
    username: v.optional(v.string()),
    grade: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Temporarily disable auth check for debugging
    // const identity = await ctx.auth.getUserIdentity();
    // if (!identity) {
    //   throw new Error("Not authenticated");
    // }
    // const userId = identity.subject;

    // For now, we'll get the userId from the args or use a fallback
    // This is temporary until we fix the auth integration
    const userId = args.userId || "temp-user-id";

    // Find the user
    const user = await ctx.db
      .query("users")
      .filter(q => q.eq(q.field("userId"), userId))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Update the user
    return await ctx.db.patch(user._id, {
      ...(args.username && { username: args.username }),
      ...(args.grade && { grade: args.grade }),
    });
  },
});