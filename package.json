{"name": "keleme-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/nextjs": "^6.23.2", "@convex-dev/auth": "^0.0.87", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.25.2", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "svix": "^1.68.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "^5"}}