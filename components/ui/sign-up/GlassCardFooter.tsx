'use client';

import Link from 'next/link';

export default function GlassCardFooter() {
  return (
    <div className="text-center mt-auto z-30 px-8 pb-8">
      {/* Sign-in Link */}
      <p className="text-sm font-normal text-white/70 mb-3">
        Already have an account?{' '}
        <Link href="/sign-in" className="font-semibold hover:opacity-80">
          Sign in here
        </Link>
      </p>

      {/* Help Links */}
      <div className="flex items-center justify-center gap-4 text-xs font-normal text-white/50">
        <Link href="/help" className="hover:text-white/70">
          Help Center
        </Link>
        <span>•</span>
        <Link href="/contact" className="hover:text-white/70">
          Contact Support
        </Link>
      
        
      </div>
    </div>
  );
}