  import { WebhookEvent } from "@clerk/nextjs/server";
import { fetchMutation } from "convex/nextjs";
import { api } from "@/convex/_generated/api";
import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { Webhook } from "svix";
  
export async function POST(req: Request) {
    const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET;
  
    if (!WEBHOOK_SECRET) {
     throw new Error(
       "Please add WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local"
     );
   }
 
   // Get the headers
   const headerPayload = await headers();
   const svix_id = headerPayload.get("svix-id");
   const svix_timestamp = headerPayload.get("svix-timestamp");
   const svix_signature = headerPayload.get("svix-signature");
 
   // If there are no headers return 400
   if (!svix_id || !svix_timestamp || !svix_signature) {
     return new Response("Error occured — no svix headers", {
       status: 400,
     });
   }
 
   // Get the body
   const payload = await req.json();
   const body = JSON.stringify(payload);
 
   // Create a new SVIX instance with your secret.
   const wh = new Webhook(WEBHOOK_SECRET);
   let evt: WebhookEvent;
 
   // Verify the payload with the headers
   try {
     evt = wh.verify(body, {
       "svix-id": svix_id,
       "svix-timestamp": svix_timestamp,
       "svix-signature": svix_signature,
     }) as WebhookEvent;
   } catch (err) {
     console.error("Error verifying webhook:", err);
     return new Response("Error occured", {
       status: 400,
     });
   }
 
   // Get the ID and type
   const { id } = evt.data;
   const eventType = evt.type;
 
   if (eventType === "user.created") {
     try {
      console.log('payload',payload);

        const userData = {
         userId: payload?.data?.id,
         email: payload?.data?.email_addresses?.[0]?.email_address,
         name: `${payload?.data?.first_name || ""} ${payload?.data?.last_name || ""}`,
         createdAt: Date.now(),
        profileImage: payload?.data?.profile_image_url,
        };
        await fetchMutation(api.users.createUser, userData);


       return NextResponse.json({
         status: 200,
         message: "User info inserted",
       });
     } catch (error: any) {
       return NextResponse.json({
         status: 400,
        message: error.message,
       });
     }
   }
 
   if (eventType === "user.updated") {
     try {
       return NextResponse.json({
         status: 200,
         message: "User info updated",
       });
     } catch (error: any) {
       return NextResponse.json({
         status: 400,
         message: error.message,
       });
     }
   }
 
   return new Response("Error occured — unhandled event type", {
     status: 400,
   });
}