'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSignUp } from '@clerk/nextjs';
import PageTransition from '../animations/PageTransition';
import AnimatedButton from '../animations/AnimatedButton';
import AnimatedInput from '../animations/AnimatedInput';

export default function VerificationForm(): React.JSX.Element {
  const router = useRouter();
  const { isLoaded, signUp, setActive } = useSignUp();
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isResending, setIsResending] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!isLoaded) return;

    if (!/^\d{6}$/.test(code)) {
      setError('Enter a valid 6-digit code.');
      return;
    }

    setError('');
    setIsSubmitting(true);

    try {
      const verification = signUp.emailAddress ?
        await signUp.attemptEmailAddressVerification({ code }) :
        await signUp.attemptPhoneNumberVerification({ code });

      await setActive({ session: verification.createdSessionId });
      router.push('/personalize');
    } catch (err: any) {
      setError(err.errors?.[0]?.message || 'Invalid verification code');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendCode = async () => {
    if (!isLoaded || isResending) return;

    setIsResending(true);
    setError('');

    try {
      if (signUp.emailAddress) {
        await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
      } else if (signUp.phoneNumber) {
        await signUp.preparePhoneNumberVerification({ strategy: 'phone_code' });
      }
      // You could add a success message here if needed
    } catch (err: any) {
      setError(err.errors?.[0]?.message || 'Failed to resend code. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  return (
    <PageTransition direction="right" duration={0.4}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-6 mt-6 mb-6">
      {/* Heading */}
      <div>
        <h2 className="text-2xl font-medium mb-2">Enter the code</h2>
        <p className="text-sm font-normal text-white/70">
          We’ve sent a 6-digit code to your email or phone. Please enter it below.
        </p>
      </div>

      {/* Input Field */}
      <AnimatedInput
        type="text"
        value={code}
        onChange={(e) => setCode(e.target.value)}
        placeholder="123456"
        error={error}
        className="text-center text-lg tracking-widest"
      />



      {/* Verify Button */}
      <AnimatedButton
        type="submit"
        isLoading={isSubmitting}
        className="w-full py-4"
        variant="primary"
      >
        Verify and Continue
        <svg
          width="16"
          height="16"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path d="M13 7l5 5-5 5M6 12h12" />
        </svg>
      </AnimatedButton>

      {/* Resend Link */}
      <p className="text-sm text-white/60 text-center">
        Didn’t receive the code?{' '}
        <AnimatedButton
          type="button"
          onClick={handleResendCode}
          variant="ghost"
          isLoading={isResending}
          className="underline text-sm p-0 h-auto"
        >
          Resend
        </AnimatedButton>
      </p>
    </form>
    </PageTransition>
  );
}