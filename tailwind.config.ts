import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: 'class',
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx}',
    './components/**/*.{js,ts,jsx,tsx}',
    './src/components/ui/**/*.{js,ts,jsx,tsx}', // shadcn location
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: { '2xl': '1400px' },
    },
    extend: {
      fontFamily: {
        inter: ['Inter', 'ui-sans-serif', 'system-ui'],
      },
      colors: {
        card: 'rgba(25,25,25,0.55)',
      },
      boxShadow: {
        glass: '0 40px 80px 0 rgba(0,0,0,0.55)',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};

export default config;