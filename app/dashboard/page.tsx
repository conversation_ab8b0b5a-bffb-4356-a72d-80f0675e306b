import { UserButton } from '@clerk/nextjs';

export default function Dashboard() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <UserButton afterSignOutUrl="/" />
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-semibold text-white mb-4">Welcome to Keleme!</h2>
          <p className="text-white/70">
            Your account has been successfully created and verified. You can now access all features of the platform.
          </p>
        </div>
      </div>
    </div>
  );
}