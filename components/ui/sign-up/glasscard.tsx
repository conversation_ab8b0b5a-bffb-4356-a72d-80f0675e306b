'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import * as React from 'react';

type GlassCardProps = {
  children?: ReactNode;
};

export default function GlassCard({ children }: GlassCardProps): React.JSX.Element {
  const pathname = usePathname();

  const steps = [
    { label: 'Sign up', href: '/sign-up' },
    { label: 'Verification', href: '/verification' },
    { label: 'Personalization', href: '/personalize' },
  ];

  const step = pathname.includes('/verification')
    ? 2
    : pathname.includes('/personalize')
    ? 3
    : 1;

  const prevStepHref = step === 3 ? '/verification' : step === 2 ? '/sign-up' : null;

  return (
    <div className="relative z-10 flex flex-col overflow-hidden cursor-default beautiful-shadow transition-custom max-w-2xl w-full font-semibold text-white rounded-3xl mx-4">
      {/* Glass layers */}
      <div className="absolute inset-0 z-0 backdrop-blur-md glass-filter isolate" />
      <div className="absolute inset-0 z-10 bg-white bg-opacity-15" />
      <div
        className="absolute inset-0 z-20 overflow-hidden shadow-inner"
        style={{
          boxShadow:
            'inset 2px 2px 1px 0 rgba(255,255,255,0.5), inset -1px -1px 1px 1px rgba(255,255,255,0.5)',
          borderRadius: '24px',
        }}
      />

      {/* Back Arrow in top-left corner */}
      {prevStepHref && (
        <Link
          href={prevStepHref}
          className="absolute top-5 left-5 z-40 p-2 rounded-md hover:bg-white/10 transition"
        >
          <svg
            className="w-5 h-5 text-white"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
        </Link>
      )}

      {/* Header */}
      <div className="z-30 flex flex-col items-center justify-center text-center bg-black/10 pt-10 px-8 pb-6">
        {/* Logo Box */}
        <div className="mb-4">
          <div className="relative inline-flex items-center justify-center w-24 h-24 rounded-2xl mb-3 overflow-hidden">
            <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
            <div className="absolute inset-0 z-10 bg-gradient-to-br from-white/30 to-white/10" />
            <div
              className="absolute inset-0 z-20"
              style={{
                boxShadow:
                  'inset 3px 3px 2px 0 rgba(255,255,255,0.6), inset -2px -2px 2px 2px rgba(255,255,255,0.4)',
                borderRadius: '16px',
              }}
            />
            <Image
              src="/logo.png"
              alt="ቀለሜ Logo"
              fill
              className="z-20 object-contain"
              sizes="96px"
            />
          </div>

          <h1 className="text-5xl font-normal tracking-tighter mb-2">
            Create your ቀለሜ account
          </h1>
          <p className="text-sm font-light text-white/80">
            Let’s get you set up with your new account in just a few simple steps.
          </p>
        </div>

        {/* Steps Navigation */}
        <div className="flex items-center gap-3">
          {steps.map((item, index) => {
            const stepNumber = index + 1;
            const isActive = step === stepNumber;

            return (
              <div key={item.label} className="flex items-center gap-2">
                {index !== 0 && <div className="w-6 h-px bg-white/30" />}
                <Link href={item.href}>
                  <div
                    className={`step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm transition-all duration-300 ${
                      isActive
                        ? 'bg-white/40 text-white'
                        : 'bg-white/10 text-white/60'
                    }`}
                  >
                    {stepNumber}
                  </div>
                </Link>
                <Link href={item.href}>
                  <span
                    className={`text-xs font-medium transition-colors duration-300 hidden sm:block ${
                      isActive ? 'text-white/90' : 'text-white/60'
                    }`}
                  >
                    {item.label}
                  </span>
                </Link>
              </div>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="z-30 px-8 pt-6 pb-8">{children}</div>
    </div>
  );
}