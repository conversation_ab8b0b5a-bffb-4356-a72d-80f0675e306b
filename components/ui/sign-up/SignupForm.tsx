'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';

interface FormData {
  firstName: string;
  lastName: string;
  contact: string;
  password: string;
  agree: boolean;
}

type FormErrors = Partial<Record<keyof FormData, string>>;

export default function SignupForm(): React.JSX.Element {
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    contact: '',
    password: '',
    agree: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);

  const nameRegex = /^[a-zA-Z\s'-]{2,}$/;
  const emailRegex = /^[^@\s]+@[^@\s]+\.[^@\s]+$/;
  const ethiopianPhoneRegex = /^(\+251|0)?9\d{8}$/;

  function validate(): boolean {
    const newErrors: FormErrors = {};

    if (!nameRegex.test(formData.firstName)) {
      newErrors.firstName = 'Enter a valid first name';
    }
    if (!nameRegex.test(formData.lastName)) {
      newErrors.lastName = 'Enter a valid last name';
    }
    if (
      !emailRegex.test(formData.contact) &&
      !ethiopianPhoneRegex.test(formData.contact)
    ) {
      newErrors.contact = 'Enter valid email or Ethiopian phone';
    }
    if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    }
    if (!formData.agree) {
      newErrors.agree = 'You must agree to continue';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (validate()) {
      window.location.href = '/verification';
    }
  }

  function handleChange(
    e: React.ChangeEvent<HTMLInputElement>
  ): void {
    const { name, value, type, checked } = e.target;
    const key = name as keyof FormData;
    setFormData((prev) => ({
      ...prev,
      [key]: type === 'checkbox' ? checked : value,
    }));
  }

  return (
    <div className="mt-6 mb-6">
      <form onSubmit={handleSubmit} className="flex flex-col gap-6">
        {/* Title */}
        <div>
          <h2 className="text-2xl font-medium mb-2">
            Tell us about yourself
          </h2>
          <p className="text-sm font-normal text-white/70">
            Step 1 of 3 • This helps us personalize your
            experience
          </p>
        </div>

        {/* First & Last Name */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {(['firstName', 'lastName'] as (keyof FormData)[]).map(
            (field, idx) => (
              <div key={field}>
                <label
                  htmlFor={field}
                  className="block text-sm font-medium mb-2"
                >
                  {idx === 0 ? 'First name' : 'Last name'}
                </label>
                <input
                  id={field}
                  name={field}
                  type="text"
                  placeholder={idx === 0 ? 'adel' : 'abdu'}
                  value={formData[field] as string}
                  onChange={handleChange}
                  className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
                />
                {errors[field] && (
                  <p className="text-red-400 text-xs mt-1">
                    {errors[field]}
                  </p>
                )}
              </div>
            )
          )}
        </div>

        {/* Email or Phone */}
        <div>
          <label
            htmlFor="contact"
            className="block text-sm font-medium mb-2"
          >
            Email or phone number
          </label>
          <input
            id="contact"
            name="contact"
            type="text"
            placeholder="<EMAIL> or +251912345678"
            value={formData.contact}
            onChange={handleChange}
            className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
          />
          {errors.contact && (
            <p className="text-red-400 text-xs mt-1">
              {errors.contact}
            </p>
          )}
        </div>

        {/* Password with Toggle */}
        <div>
          <label
            htmlFor="password"
            className="block text-sm font-medium mb-2"
          >
            Create password
          </label>
          <div className="relative">
            <input
              id="password"
              name="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Minimum 8 characters"
              value={formData.password}
              onChange={handleChange}
              className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
            />
            <button
              type="button"
              onClick={() => setShowPassword((v) => !v)}
              className="absolute right-3 top-3 text-white"
              aria-label="Toggle password visibility"
            >
              {showPassword ? (
                // Eye-off icon
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-5.523 0-10-4.03-10-7s4.477-7 10-7c2.35 0 4.507.838 6.125 2.225M3 3l18 18"
                  />
                </svg>
              ) : (
                // Eye icon
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-400 text-xs mt-1">
              {errors.password}
            </p>
          )}
        </div>

        {/* Terms */}
        <div className="flex items-start gap-2">
          <input
            id="agree"
            name="agree"
            type="checkbox"
            checked={formData.agree}
            onChange={handleChange}
            className="mt-1 h-4 w-4"
          />
          <label htmlFor="agree" className="text-sm text-white/70">
            I agree to the{' '}
            <Link href="/terms" className="underline font-medium">
              Terms of Service
            </Link>{' '}
            and{' '}
            <Link
              href="/privacy-policy"
              className="underline font-medium"
            >
              Privacy Policy
            </Link>
          </label>
        </div>
        {errors.agree && (
          <p className="text-red-400 text-xs mt-1">
            {errors.agree}
          </p>
        )}

        {/* Submit */}
        <button
          type="submit"
          className="mt-4 py-4 text-base font-semibold text-white bg-gradient-to-r from-white/30 to-white/10 rounded-xl backdrop-blur-sm"
        >
          Continue to Step 2 →
        </button>
      </form>
    </div>
  );
}