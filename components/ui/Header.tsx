// components/header.tsx
"use client";

import {
  SignIn<PERSON><PERSON><PERSON>,
  SignUp<PERSON>utton,
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";

export default function Header() {
  return (
    <header className="flex justify-end items-center p-4 gap-4 border-b">
      <SignedOut>
        <SignInButton />
        <SignUpButton>
          <button className="bg-black text-white rounded px-4 py-2">
            Sign Up
          </button>
        </SignUpButton>
      </SignedOut>
      <SignedIn>
        <UserButton />
      </SignedIn>
    </header>
  );
}