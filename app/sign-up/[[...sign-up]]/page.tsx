"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSignUp } from "@clerk/nextjs";
import particlesJS from "particles.js"; // install via `npm install particles.js`

export default function SignUpPage() {
  const router = useRouter();
  const { isLoaded, signUp, setSession } = useSignUp();
  const [firstName, setFirstName] = useState("");
  const [lastName,  setLastName]  = useState("");
  const [email,     setEmail]     = useState("");
  const [password,  setPassword]  = useState("");
  const [agree,     setAgree]     = useState(false);
  const [error,     setError]     = useState<string | null>(null);

  // Initialize particles.js once on mount
  useEffect(() => {
    particlesJS("particles-js", {
      particles: {
        number: { value: 80, density: { enable: true, value_area: 800 } },
        color: { value: "#d4d4d8" },
        shape: { type: "circle" },
        opacity: { value: 0.4 },
        size: { value: 3, random: true },
        line_linked: {
          enable: true,
          distance: 150,
          color: "#a1a1aa",
          opacity: 0.3,
          width: 1,
        },
        move: {
          enable: true,
          speed: 2,
          out_mode: "out",
        },
      },
      interactivity: {
        events: {
          onhover: { enable: true, mode: "repulse" },
          onclick: { enable: true, mode: "push" },
        },
        modes: {
          repulse: { distance: 100, duration: 0.4 },
          push:   { particles_nb: 4 },
        },
      },
      retina_detect: true,
    });
  }, []);

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!isLoaded) return;
    if (!agree) {
      setError("You must agree to the Terms of Service");
      return;
    }

    try {
      // 1) create the user
      await signUp.create({
        identifier: email,
        password,
        firstName,
        lastName,
      });

      // 2) verify via email code
      const result = await signUp.prepareEmailAddressVerification({
        strategy: "email_code",
      });

      // 3) establish a session so they’re logged in
      await setSession({ session: result.createdSessionId });

      router.push("/");
    } catch (err: any) {
      setError(err.errors?.[0]?.longMessage || err.message);
    }
  }

  return (
    <div className="min-h-screen bg-neutral-100 flex items-center justify-center p-4 relative">
      {/* Particles background */}
      <div
        id="particles-js"
        className="absolute inset-0 z-0"
      />

      <div className="relative z-10 max-w-4xl w-full bg-white rounded-2xl overflow-hidden beautiful-shadow border border-neutral-200 flex flex-col md:flex-row">
        {/* Left column */}
        <div className="md:w-1/2 h-72 md:h-auto relative bg-gradient-to-br from-neutral-900 to-neutral-800">
          <div className="absolute top-6 left-6 z-10">
            <span className="px-3 py-1 bg-neutral-700/80 rounded-full text-xs text-neutral-300 mb-3 inline-block">NETWORK</span>
            <h2 className="heading-font text-3xl text-white">Join Our</h2>
            <h2 className="heading-font text-3xl text-white">Community</h2>
            <div className="h-1 w-16 bg-neutral-400 mt-3 rounded-full"></div>
          </div>
          <div className="absolute bottom-6 left-6 bg-neutral-800/80 backdrop-blur-sm rounded-lg px-4 py-3 z-10 border border-neutral-700">
            <div className="text-xs text-neutral-400 mb-1">Active Members</div>
            <div className="heading-font text-lg text-neutral-200">12,847</div>
          </div>
        </div>

        {/* Right column */}
        <div className="md:w-1/2 p-8 bg-white">
          <span className="px-3 py-1 bg-neutral-100 rounded-full text-xs text-neutral-500 mb-4 inline-block">GET STARTED</span>
          <h3 className="heading-font text-2xl text-neutral-800 mb-2">Create Account</h3>
          <p className="text-neutral-500 text-sm mb-8">
            Join thousands of professionals in our growing network
          </p>

          <form onSubmit={handleSubmit} className="space-y-5">
            {error && <div className="text-red-600 text-sm">{error}</div>}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-neutral-600 mb-2">FIRST NAME</label>
                <input
                  type="text"
                  value={firstName}
                  onChange={e => setFirstName(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-neutral-600 mb-2">LAST NAME</label>
                <input
                  type="text"
                  value={lastName}
                  onChange={e => setLastName(e.target.value)}
                  required
                  className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-neutral-600 mb-2">EMAIL ADDRESS</label>
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
                className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-neutral-600 mb-2">PASSWORD</label>
              <input
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
                required
                className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
              />
            </div>

            <div className="flex items-start">
              <input
                type="checkbox"
                checked={agree}
                onChange={e => setAgree(e.target.checked)}
                className="w-4 h-4 bg-neutral-50 border border-neutral-300 rounded mt-0.5"
              />
              <label className="ml-3 text-xs text-neutral-500">
                I agree to the{" "}
                <a href="#" className="text-neutral-700 underline">Terms of Service</a>{" "}
                and{" "}
                <a href="#" className="text-neutral-700 underline">Privacy Policy</a>
              </label>
            </div>

            <button
              type="submit"
              className="w-full px-6 py-3 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm flex items-center justify-center"
            >
              Create Account
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 ml-2"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </form>

          <div className="mt-6 pt-6 border-t border-neutral-200 text-center text-xs text-neutral-500">
            Already have an account?{" "}
            <a href="/sign-in" className="text-neutral-700 underline">
              Sign in
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}