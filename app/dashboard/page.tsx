'use client';

import { UserButton } from '@clerk/nextjs';
import { useState, useEffect } from 'react';
import WelcomeAnimation from '@/components/ui/animations/WelcomeAnimation';

export default function Dashboard() {
  const [showWelcome, setShowWelcome] = useState(true);

  // Check if user has seen welcome animation before
  useEffect(() => {
    const hasSeenWelcome = localStorage.getItem('keleme-welcome-seen');
    if (hasSeenWelcome) {
      setShowWelcome(false);
    }
  }, []);

  const handleWelcomeComplete = () => {
    setShowWelcome(false);
    localStorage.setItem('keleme-welcome-seen', 'true');
  };
  return (
    <>
      {showWelcome && (
        <WelcomeAnimation onComplete={handleWelcomeComplete} />
      )}
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-white">Dashboard</h1>
          <UserButton />
        </div>

        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
          <h2 className="text-xl font-semibold text-white mb-4">Welcome to Keleme!</h2>
          <p className="text-white/70">
            Your account has been successfully created and verified. You can now access all features of the platform.
          </p>
        </div>
      </div>
    </div>
    </>
  );
}