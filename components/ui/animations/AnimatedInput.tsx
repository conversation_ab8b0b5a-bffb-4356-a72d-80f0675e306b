'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface AnimatedInputProps {
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  className?: string;
  error?: string;
  disabled?: boolean;
  autoComplete?: string;
}

const inputVariants = {
  idle: { scale: 1, boxShadow: '0 0 0 0px rgba(255,255,255,0.1)' },
  focus: { 
    scale: 1.01, 
    boxShadow: '0 0 0 2px rgba(255,255,255,0.2)',
    transition: { duration: 0.2 }
  }
};

const errorVariants = {
  hidden: { opacity: 0, y: -10, height: 0 },
  visible: { 
    opacity: 1, 
    y: 0, 
    height: 'auto',
    transition: { duration: 0.3, ease: 'easeOut' }
  }
};

export default function AnimatedInput({
  type = 'text',
  placeholder,
  value,
  onChange,
  onBlur,
  className = '',
  error,
  disabled = false,
  autoComplete
}: AnimatedInputProps) {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  return (
    <div className="relative">
      {/* Input Container */}
      <motion.div
        className="relative overflow-hidden rounded-xl"
        variants={inputVariants}
        animate={isFocused ? 'focus' : 'idle'}
      >
        {/* Glass morphism background layers */}
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow: isFocused 
              ? 'inset 1px 1px 1px 0 rgba(255,255,255,.4), inset -1px -1px 1px 1px rgba(255,255,255,.2)'
              : 'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
            borderRadius: '12px',
          }}
        />
        
        {/* Input Field */}
        <input
          type={type}
          placeholder={placeholder}
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
          autoComplete={autoComplete}
          className={`
            relative z-30 w-full px-4 py-3 bg-transparent text-white placeholder-white/50
            border-none outline-none transition-all duration-200
            disabled:opacity-50 disabled:cursor-not-allowed
            ${className}
          `}
        />

        {/* Focus glow effect */}
        <motion.div
          className="absolute inset-0 z-5 rounded-xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: isFocused ? 1 : 0 }}
          transition={{ duration: 0.2 }}
          style={{
            background: 'linear-gradient(45deg, rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.1))',
            filter: 'blur(1px)'
          }}
        />
      </motion.div>

      {/* Error Message */}
      <motion.div
        variants={errorVariants}
        initial="hidden"
        animate={error ? 'visible' : 'hidden'}
        className="overflow-hidden"
      >
        {error && (
          <motion.p 
            className="text-red-400 text-sm mt-2 px-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {error}
          </motion.p>
        )}
      </motion.div>
    </div>
  );
}
