'use client';

import React, { useState } from 'react';

export default function PersonalizedForm(): JSX.Element {
  const [username, setUsername] = useState('');
  const [grade, setGrade] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!username.trim()) {
      setError('Username is required.');
      return;
    }

    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      setError('Username must be 3–20 characters long and contain only letters, numbers, or underscores.');
      return;
    }

    if (!grade) {
      setError('Please select your grade.');
      return;
    }

    setError('');
    // Submit logic or redirect
    alert('Account setup completed!');
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6 mt-6 mb-6">
      {/* Title */}
      <div>
        <h2 className="text-2xl font-medium mb-2">Personalize your profile</h2>
        <p className="text-sm font-normal text-white/70">
          Let us know your preferred username and current grade level.
        </p>
      </div>

      {/* Username Input */}
      <div className="relative overflow-hidden rounded-xl">
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow:
              'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
            borderRadius: '12px',
          }}
        />
        <input
          type="text"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          placeholder="Username"
          className="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
        />
      </div>

      {/* Grade Selector */}
      <div className="relative overflow-hidden rounded-xl">
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow:
              'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
            borderRadius: '12px',
          }}
        />
        <select
          value={grade}
          onChange={(e) => setGrade(e.target.value)}
          className="z-30 relative w-full bg-transparent px-4 py-3 text-sm text-white focus:outline-none"
        >
          <option value="">Select your grade</option>
          <option value="9">Grade 9</option>
          <option value="10">Grade 10</option>
          <option value="11">Grade 11</option>
          <option value="12">Grade 12</option>
        </select>
      </div>

      {/* Error */}
      {error && <p className="text-red-400 text-xs mt-1">{error}</p>}

      {/* Finish Button */}
      <div className="relative overflow-hidden rounded-xl transition-custom hover:shadow-lg mt-2">
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-gradient-to-r from-white/30 to-white/20" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow:
              'inset 2px 2px 1px 0 rgba(255,255,255,.5), inset -1px -1px 1px 1px rgba(255,255,255,.3)',
            borderRadius: '12px',
          }}
        />
        <button
          type="submit"
          className="z-30 relative w-full py-4 flex items-center justify-center gap-2 text-base font-semibold text-white bg-transparent"
        >
          Finish
          <svg
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path d="M13 7l5 5-5 5M6 12h12" />
          </svg>
        </button>
      </div>
    </form>
  );
}