/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './pages/**/*.{js,ts,jsx,tsx}', 
    './components/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
        'instrument-serif': ['Instrument Serif', 'serif'],
        bricolage: ['Bricolage Grotesque', 'sans-serif'],
      },
      colors: {
        brand: '#3b82f6',
        surface: { 900: '#090909', 800: '#111111', 700: '#181818' },
      },
      borderRadius: { xl2: '1.5rem' },
    },
  },
  plugins: [],
};