import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

export const createUser = mutation({
  args: {
    userId: v.string(),
    email: v.string(),
    name: v.string(),
    createdAt: v.number(),
    profileImage: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      const newUser = await ctx.db.insert("users", {
        userId: args.userId,
        email: args.email,
        name: args.name,
        createdAt: args.createdAt,
        profileImg: args.profileImage,
      });

      return newUser;
    } catch (error) {
      throw new Error("User information did not insert successfully");
    }
  },
});

export const readUser = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userInfo = await ctx.db
        .query("users")
        .filter((user) => user.eq(user.field("userId"), args.userId))
        .first();

      return userInfo;
    } catch (error) {
      throw new Error("Reading user did not work");
    }
  },
});

export const update = mutation({
  args: {
    userId: v.optional(v.string()),
    username: v.optional(v.string()),
    grade: v.optional(v.string()),
    name: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Use userId from args since we're passing it explicitly
    const userId = args.userId;

    if (!userId) {
      throw new Error("User ID is required");
    }

    // Find the user
    const user = await ctx.db
      .query("users")
      .filter(q => q.eq(q.field("userId"), userId))
      .first();

    if (!user) {
      throw new Error("User not found");
    }

    // Update the user
    return await ctx.db.patch(user._id, {
      ...(args.username && { username: args.username }),
      ...(args.grade && { grade: args.grade }),
      ...(args.name && { name: args.name }),
    });
  },
});