// app/layout.tsx
import type { Metadata } from "next";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignI<PERSON><PERSON><PERSON><PERSON>,
  Sign<PERSON>p<PERSON>utton,
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";
import { Providers } from "./providers"; // 👈 your React Query wrapper
import "./globals.css";

export const metadata: Metadata = {
  title: "keleme",
  description: "keleme Web App",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body>
          <Providers>
            <header className="flex justify-end items-center p-4 gap-4 border-b">
              <SignedOut>
                <SignInButton />
                <SignUpButton>
                  <button className="bg-black text-white rounded px-4 py-2">
                    Sign Up
                  </button>
                </SignUpButton>
              </SignedOut>
              <SignedIn>
                <UserButton />
              </SignedIn>
            </header>
            {children}
          </Providers>
        </body>
      </html>
    </ClerkProvider>
  );
}