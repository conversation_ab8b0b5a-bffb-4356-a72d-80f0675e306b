import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// ✅ Define public routes — these won't require authentication
const isPublicRoute = createRouteMatcher([
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/verification',
  '/personalize' ,
  '/api/webhook/clerk', // 👈 This is your Clerk webhook endpoint
]);

// ✅ Middleware logic to protect all other routes
export default clerkMiddleware(async (auth, request) => {
  if (!isPublicRoute(request)) {
    await auth();
  }
});

// ✅ Middleware config to apply to all API routes and skip static files
export const config = {
  matcher: [
    '/((?!_next|.*\\.(?:ico|png|jpg|jpeg|svg|css|js|woff2?|ttf|eot|json|txt|pdf|csv|xlsx?|zip|webmanifest)).*)',
    '/(api|trpc)(.*)', // run on all API routes
  ],
};