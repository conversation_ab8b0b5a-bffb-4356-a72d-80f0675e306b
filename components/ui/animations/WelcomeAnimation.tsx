'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useUser } from '@clerk/nextjs';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

interface WelcomeAnimationProps {
  onComplete?: () => void;
}

export default function WelcomeAnimation({ onComplete }: WelcomeAnimationProps) {
  const { user, isLoaded } = useUser();
  const userData = useQuery(api.users.readUser, user?.id ? { userId: user.id } : "skip");
  const [currentStep, setCurrentStep] = useState(0);
  const [showWelcome, setShowWelcome] = useState(true);

  // Get the display name (username or firstName)
  const displayName = userData?.username || user?.firstName || 'there';

  const welcomeSteps = [
    {
      title: "Welcome back!",
      subtitle: `Hello, ${displayName}`,
      icon: "👋",
      delay: 0
    },
    {
      title: "Ready to explore?",
      subtitle: "Let's discover what <PERSON><PERSON><PERSON> has to offer",
      icon: "🚀",
      delay: 2000
    },
    {
      title: "Your journey begins now",
      subtitle: "Click anywhere to continue",
      icon: "✨",
      delay: 4000
    }
  ];

  useEffect(() => {
    if (!isLoaded || !showWelcome) return;

    const timers = welcomeSteps.map((step, index) => 
      setTimeout(() => {
        setCurrentStep(index);
      }, step.delay)
    );

    // Auto-hide after all steps
    const autoHideTimer = setTimeout(() => {
      handleComplete();
    }, 7000);

    return () => {
      timers.forEach(clearTimeout);
      clearTimeout(autoHideTimer);
    };
  }, [isLoaded, showWelcome]);

  const handleComplete = () => {
    setShowWelcome(false);
    onComplete?.();
  };

  if (!isLoaded || !showWelcome) {
    return null;
  }

  const currentWelcomeStep = welcomeSteps[currentStep];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-purple-900/95 via-blue-900/95 to-indigo-900/95 backdrop-blur-sm cursor-pointer"
        onClick={handleComplete}
      >
        <motion.div
          className="text-center max-w-md mx-auto px-8"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          {/* Icon Animation */}
          <motion.div
            key={`icon-${currentStep}`}
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ 
              duration: 0.6, 
              ease: "easeOut",
              type: "spring",
              stiffness: 200
            }}
            className="text-6xl mb-6"
          >
            {currentWelcomeStep.icon}
          </motion.div>

          {/* Title Animation */}
          <motion.h1
            key={`title-${currentStep}`}
            initial={{ y: 30, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-4xl font-bold text-white mb-4"
          >
            {currentWelcomeStep.title}
          </motion.h1>

          {/* Subtitle Animation */}
          <motion.p
            key={`subtitle-${currentStep}`}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-xl text-white/80 mb-8"
          >
            {currentWelcomeStep.subtitle}
          </motion.p>

          {/* Progress Dots */}
          <div className="flex justify-center space-x-2 mb-8">
            {welcomeSteps.map((_, index) => (
              <motion.div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep ? 'bg-white' : 'bg-white/30'
                }`}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 }}
              />
            ))}
          </div>

          {/* Call to Action */}
          {currentStep === welcomeSteps.length - 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="space-y-4"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleComplete}
                className="px-8 py-3 bg-white/20 backdrop-blur-sm rounded-xl border border-white/30 text-white font-semibold hover:bg-white/30 transition-colors"
              >
                Let's Get Started
              </motion.button>
              
              <p className="text-sm text-white/60">
                or click anywhere to continue
              </p>
            </motion.div>
          )}
        </motion.div>

        {/* Floating Particles */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full"
              initial={{
                x: Math.random() * window.innerWidth,
                y: Math.random() * window.innerHeight,
                opacity: 0
              }}
              animate={{
                y: [null, -100],
                opacity: [0, 1, 0]
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2
              }}
            />
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}
