// app/sign-up/[[...sign-up]]/page.tsx
"use client";

import dynamic from "next/dynamic";
import { useCallback, useState, FormEvent } from "react";
import { useSignUp } from "@clerk/nextjs";
import type { Engine } from "@tsparticles/engine";
import { loadFull } from "@tsparticles/engine";

// 1️⃣ Dynamically import the Particles component (client-only)
const Particles = dynamic(
  () => import("@tsparticles/react").then((m) => m.Particles),
  { ssr: false }
);

// 2️⃣ Your particles config
const particlesOptions = {
  particles: {
    number: { value: 80, density: { enable: true, value_area: 800 } },
    color:  { value: "#d4d4d8" },
    shape:  { type: "circle" },
    opacity:{ value: 0.4 },
    size:   { value: 3, random: true },
    line_linked: {
      enable: true,
      distance: 150,
      color: "#a1a1aa",
      opacity: 0.3,
      width: 1,
    },
    move: {
      enable: true,
      speed: 2,
      out_mode: "out",
    },
  },
  interactivity: {
    events: {
      onhover: { enable: true, mode: "repulse" },
      onclick: { enable: true, mode: "push" },
    },
    modes: {
      repulse: { distance: 100, duration: 0.4 },
      push: { particles_nb: 4 },
    },
  },
  retina_detect: true,
};

export default function SignUpPage() {
  // Clerk sign-up hook
  const { isLoaded, signUp, setSession } = useSignUp();

  // Form state
  const [firstName, setFirstName] = useState("");
  const [lastName,  setLastName]  = useState("");
  const [email,     setEmail]     = useState("");
  const [password,  setPassword]  = useState("");
  const [agree,     setAgree]     = useState(false);
  const [error,     setError]     = useState<string | null>(null);

  // 3️⃣ Initialize the tsParticles engine (loads all presets)
  const particlesInit = useCallback(async (engine: Engine) => {
    await loadFull(engine);
  }, []);

  // Form submit handler
  async function handleSubmit(e: FormEvent) {
    e.preventDefault();
    if (!agree) {
      setError("Please accept the terms to continue.");
      return;
    }
    if (!isLoaded) return; // Clerk still loading

    try {
      // Create user
      const { createdSessionId } = await signUp.create({
        firstName,
        lastName,
        identifier: email,
        password,
      });

      // Establish session
      await setSession({ session: createdSessionId });
      // Redirect (change as you like)
      window.location.href = "/";
    } catch (err: any) {
      setError(err.errors?.[0]?.longMessage || err.message);
    }
  }

  return (
    <div className="bg-neutral-100 min-h-screen flex items-center justify-center p-4">
      <div className="max-w-4xl w-full bg-white rounded-2xl overflow-hidden shadow-lg flex flex-col md:flex-row">
        {/* ─── Left column with particles ─────────────────────────── */}
        <div className="md:w-1/2 h-72 md:h-auto relative bg-gradient-to-br from-neutral-900 to-neutral-800">
          <Particles init={particlesInit} options={particlesOptions} className="absolute inset-0" />
          {/* Overlay content */}
          <div className="absolute top-6 left-6 z-10">
            <span className="px-3 py-1 bg-neutral-700/80 rounded-full text-xs text-neutral-300 inline-block mb-3">
              NETWORK
            </span>
            <h2 className="text-3xl text-white font-semibold">Join Our</h2>
            <h2 className="text-3xl text-white font-semibold">Community</h2>
            <div className="h-1 w-16 bg-neutral-400 mt-3 rounded-full"></div>
          </div>
          <div className="absolute bottom-6 left-6 z-10 bg-neutral-800/80 backdrop-blur-sm rounded-lg px-4 py-3 border border-neutral-700">
            <div className="text-xs text-neutral-400">Active Members</div>
            <div className="text-lg text-neutral-200 font-semibold">12,847</div>
          </div>
        </div>

        {/* ─── Right column: sign-up form ────────────────────────── */}
        <div className="md:w-1/2 p-8">
          <span className="inline-block bg-neutral-100 text-xs text-neutral-500 px-3 py-1 rounded-full mb-4">
            GET STARTED
          </span>
          <h3 className="text-2xl font-semibold text-neutral-800 mb-2">Create Account</h3>
          <p className="text-sm text-neutral-500 mb-8">
            Join thousands of professionals in our growing network
          </p>

          {error && <p className="mb-4 text-sm text-red-600">{error}</p>}

          <form className="space-y-5" onSubmit={handleSubmit}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-xs font-medium text-neutral-600 mb-2">FIRST NAME</label>
                <input
                  type="text"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-neutral-600 mb-2">LAST NAME</label>
                <input
                  type="text"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                  placeholder="Doe"
                  required
                />
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium text-neutral-600 mb-2">EMAIL ADDRESS</label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-neutral-600 mb-2">PASSWORD</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-4 py-3 bg-neutral-50 border border-neutral-200 rounded-lg text-sm"
                placeholder="••••••••"
                required
              />
            </div>

            <div className="flex items-start">
              <input
                id="terms"
                type="checkbox"
                checked={agree}
                onChange={() => setAgree(!agree)}
                className="w-4 h-4 mt-1"
              />
              <label htmlFor="terms" className="ml-3 text-xs text-neutral-500">
                I agree to the <span className="underline">Terms of Service</span>{" "}
                and <span className="underline">Privacy Policy</span>
              </label>
            </div>

            <button
              type="submit"
              disabled={!agree}
              className="w-full px-6 py-3 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium flex items-center justify-center"
            >
              Create Account
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z"
                />
              </svg>
            </button>
          </form>

          <div className="mt-6 pt-6 border-t border-neutral-200 text-center text-xs text-neutral-500">
            Already have an account?{" "}
            <a href="/sign-in" className="underline text-neutral-700">
              Sign in
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}