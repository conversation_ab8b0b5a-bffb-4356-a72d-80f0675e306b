'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface AnimatedButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  className?: string;
  isLoading?: boolean;
  variant?: 'primary' | 'secondary' | 'ghost';
}

const buttonVariants = {
  idle: { scale: 1 },
  hover: { scale: 1.02 },
  tap: { scale: 0.98 }
};

const loadingVariants = {
  animate: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear"
    }
  }
};

export default function AnimatedButton({
  children,
  onClick,
  type = 'button',
  disabled = false,
  className = '',
  isLoading = false,
  variant = 'primary'
}: AnimatedButtonProps) {
  const baseClasses = `
    relative overflow-hidden rounded-xl px-6 py-3 font-medium text-white
    transition-all duration-200 ease-out
    focus:outline-none focus:ring-2 focus:ring-white/20 focus:ring-offset-2 focus:ring-offset-transparent
    disabled:opacity-50 disabled:cursor-not-allowed
  `;

  const variantClasses = {
    primary: `
      bg-gradient-to-r from-purple-600 to-blue-600 
      hover:from-purple-700 hover:to-blue-700
      shadow-lg hover:shadow-xl
    `,
    secondary: `
      bg-white/10 backdrop-blur-sm border border-white/20
      hover:bg-white/20
    `,
    ghost: `
      bg-transparent hover:bg-white/10
      border border-transparent hover:border-white/20
    `
  };

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      variants={buttonVariants}
      initial="idle"
      whileHover={!disabled && !isLoading ? "hover" : "idle"}
      whileTap={!disabled && !isLoading ? "tap" : "idle"}
      transition={{ duration: 0.15 }}
    >
      {/* Glass morphism overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-200" />
      
      {/* Content */}
      <div className="relative flex items-center justify-center gap-2">
        {isLoading && (
          <motion.div
            className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full"
            variants={loadingVariants}
            animate="animate"
          />
        )}
        <span className={isLoading ? 'opacity-70' : ''}>{children}</span>
      </div>
    </motion.button>
  );
}
