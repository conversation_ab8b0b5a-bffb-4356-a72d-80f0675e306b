'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useSignUp } from '@clerk/nextjs';
import PrimaryButton from './PrimaryButton';


interface FormData {
  firstName: string;
  lastName: string;
  contact: string;
  password: string;
  agree: boolean;
}

type FormErrors = Partial<Record<keyof FormData, string>>;

export default function SignupForm(): React.JSX.Element {
  const router = useRouter();
  const { isLoaded, signUp } = useSignUp();
  const [subStep, setSubStep] = useState<1 | 2>(1);
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    contact: '',
    password: '',
    agree: false,
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // Debug: Log component state changes and check for unexpected errors
  React.useEffect(() => {
    console.log('=== SIGNUP FORM STATE ===');
    console.log('subStep:', subStep);
    console.log('errors:', errors);
    console.log('formData:', formData);

    // Check for unexpected errors in name fields
    if (errors.firstName && !errors.firstName.includes('valid first name')) {
      console.warn('⚠️ UNEXPECTED ERROR IN FIRSTNAME:', errors.firstName);
    }
    if (errors.lastName && !errors.lastName.includes('valid last name')) {
      console.warn('⚠️ UNEXPECTED ERROR IN LASTNAME:', errors.lastName);
    }
  }, [subStep, errors, formData]);

  const nameRegex = /^[a-zA-Z\s'-]{2,}$/;
  const emailRegex = /^[^@\s]+@[^@\s]+\.[^@\s]+$/;
  const ethiopianPhoneRegex = /^(\+251|0)?9\d{8}$/;

  // Clerk password requirements: at least 8 characters, with uppercase, lowercase, number, and special character
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

  function validateStep1(): boolean {
    const newErrors: FormErrors = {};

    console.log('Validating step 1...');
    console.log('firstName:', formData.firstName, 'valid:', nameRegex.test(formData.firstName));
    console.log('lastName:', formData.lastName, 'valid:', nameRegex.test(formData.lastName));

    if (!nameRegex.test(formData.firstName)) {
      newErrors.firstName = 'Enter a valid first name';
    }
    if (!nameRegex.test(formData.lastName)) {
      newErrors.lastName = 'Enter a valid last name';
    }

    console.log('Step 1 errors:', newErrors);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function validateStep2(): boolean {
    const newErrors: FormErrors = {};

    if (
      !emailRegex.test(formData.contact) &&
      !ethiopianPhoneRegex.test(formData.contact)
    ) {
      newErrors.contact = 'Enter valid email or Ethiopian phone';
    }
    if (!passwordRegex.test(formData.password)) {
      newErrors.password = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character (@$!%*?&)';
    }
    if (!formData.agree) {
      newErrors.agree = 'You must agree to continue';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }

  function handleContinueToStep2() {
    console.log('=== STEP 1 VALIDATION DEBUG ===');
    console.log('Form data:', formData);
    console.log('Current errors:', errors);

    if (validateStep1()) {
      console.log('Step 1 validation passed, moving to step 2');
      // Clear any previous errors when moving to step 2
      setErrors({});
      setSubStep(2);
    } else {
      console.log('Step 1 validation failed');
    }
  }

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    if (!isLoaded) return;

    if (validateStep2()) {
      try {
        await signUp.create({
          firstName: formData.firstName,
          lastName: formData.lastName,
          emailAddress: emailRegex.test(formData.contact) ? formData.contact : undefined,
          phoneNumber: ethiopianPhoneRegex.test(formData.contact) ? formData.contact : undefined,
          password: formData.password,
        });

        if (emailRegex.test(formData.contact)) {
          await signUp.prepareEmailAddressVerification({ strategy: 'email_code' });
        } else {
          await signUp.preparePhoneNumberVerification({ strategy: 'phone_code' });
        }

        router.push('/verification');
      } catch (err: any) {
        console.error('=== CLERK ERROR DEBUG ===');
        console.error('Full error:', err);
        console.error('Error errors array:', err.errors);

        // Handle specific Clerk error types
        if (err.errors && err.errors.length > 0) {
          const clerkError = err.errors[0];
          console.error('First clerk error:', clerkError);
          console.error('Error code:', clerkError.code);
          console.error('Error message:', clerkError.message);
          console.error('Error meta:', clerkError.meta);
          console.error('Error paramName:', clerkError.meta?.paramName);

          let errorMessage = clerkError.message;

          // Map specific error codes to user-friendly messages
          if (clerkError.code === 'form_identifier_exists') {
            errorMessage = 'This email address is already registered. Please try signing in instead.';
          } else if (clerkError.code === 'form_password_pwned') {
            errorMessage = 'This password has been found in a data breach. Please choose a different password.';
          } else if (clerkError.code === 'form_password_validation_failed' || clerkError.message.includes('password is not strong enough')) {
            errorMessage = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character (@$!%*?&)';
          }

          // Determine which field to show the error on
          let fieldName = clerkError.meta?.paramName || 'contact';
          console.error('Original field name from Clerk:', fieldName);

          // Map Clerk field names to our form field names
          if (fieldName === 'emailAddress' || fieldName === 'phoneNumber') {
            fieldName = 'contact';
          } else if (fieldName === 'first_name') {
            fieldName = 'firstName';
          } else if (fieldName === 'last_name') {
            fieldName = 'lastName';
          }

          console.error('Mapped field name:', fieldName);

          if (fieldName === 'password' || clerkError.message.includes('password')) {
            console.error('Setting password error:', errorMessage);
            setErrors({ password: errorMessage });
          } else if (fieldName === 'firstName' || fieldName === 'lastName') {
            // Safeguard: Don't set email/contact errors to name fields
            if (errorMessage.toLowerCase().includes('email') || errorMessage.toLowerCase().includes('identifier')) {
              console.warn('⚠️ Preventing email error from being set to name field. Setting to contact instead.');
              setErrors({ contact: errorMessage });
            } else {
              console.error('Setting name field error:', fieldName, errorMessage);
              setErrors({ [fieldName]: errorMessage });
            }
          } else {
            console.error('Setting contact error:', errorMessage);
            setErrors({ contact: errorMessage });
          }
        } else {
          setErrors({
            contact: err.message || 'An error occurred during sign-up'
          });
        }
      }
    }
  }

  function handleBackToStep1() {
    setSubStep(1);
    // Clear errors when going back
    setErrors({});
  }

  function handleChange(
    e: React.ChangeEvent<HTMLInputElement>
  ): void {
    const { name, value, type, checked } = e.target;
    const key = name as keyof FormData;
    setFormData((prev) => ({
      ...prev,
      [key]: type === 'checkbox' ? checked : value,
    }));
  }

  function handleBlur(e: React.FocusEvent<HTMLInputElement>): void {
    const { name } = e.target;
    const key = name as keyof FormData;
    const newErrors: FormErrors = { ...errors };

    console.log('=== BLUR VALIDATION ===');
    console.log('Field:', key);
    console.log('Current errors before blur:', errors);

    // Validate individual fields on blur
    if (key === 'firstName' && !nameRegex.test(formData.firstName)) {
      newErrors.firstName = 'Enter a valid first name';
    } else if (key === 'firstName') {
      delete newErrors.firstName;
    }

    if (key === 'lastName' && !nameRegex.test(formData.lastName)) {
      newErrors.lastName = 'Enter a valid last name';
    } else if (key === 'lastName') {
      delete newErrors.lastName;
    }

    if (key === 'contact' &&
        !emailRegex.test(formData.contact) &&
        !ethiopianPhoneRegex.test(formData.contact)) {
      newErrors.contact = 'Enter valid email or Ethiopian phone';
    } else if (key === 'contact') {
      delete newErrors.contact;
    }

    if (key === 'password' && !passwordRegex.test(formData.password)) {
      newErrors.password = 'Password must be at least 8 characters with uppercase, lowercase, number, and special character (@$!%*?&)';
    } else if (key === 'password') {
      delete newErrors.password;
    }

    console.log('New errors after blur:', newErrors);
    setErrors(newErrors);
  }

  return (
    <div className="mt-4 mb-4">
      {/* Title */}
      <div className="mb-4">
        <h2 className="text-2xl font-medium mb-2">
          Tell us about yourself
        </h2>
        <p className="text-sm font-normal text-white/70">
          Step 1 of 3 • This helps us personalize your experience
        </p>
      </div>

      {/* Micro-step 1: Name fields */}
      {subStep === 1 && (
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(['firstName', 'lastName'] as (keyof FormData)[]).map(
              (field, idx) => (
                <div key={field}>
                  <label
                    htmlFor={field}
                    className="block text-sm font-medium mb-2"
                  >
                    {idx === 0 ? 'First name' : 'Last name'}
                  </label>
                  <input
                    id={field}
                    name={field}
                    type="text"
                    placeholder={idx === 0 ? 'adel' : 'abdu'}
                    value={formData[field] as string}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
                  />
                  {errors[field] && (
                    <p className="text-red-400 text-xs mt-1">
                      {errors[field]}
                    </p>
                  )}
                </div>
              )
            )}
          </div>

          <PrimaryButton
            onClick={handleContinueToStep2}
            className="mt-4 w-full"
          >
            Continue
          </PrimaryButton>
        </div>
      )}

      {/* Micro-step 2: Contact, Password, Terms */}
      {subStep === 2 && (
        <form onSubmit={handleSubmit} className="flex flex-col gap-4">
          {/* Back button */}
          <button
            type="button"
            onClick={handleBackToStep1}
            className="flex items-center gap-2 text-white/70 hover:text-white text-sm mb-2"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
            Back
          </button>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Email or Phone */}
            <div>
              <label
                htmlFor="contact"
                className="block text-sm font-medium mb-2"
              >
                Email or phone number
              </label>
              <input
                id="contact"
                name="contact"
                type="text"
                placeholder="<EMAIL> or +251912345678"
                value={formData.contact}
                onChange={handleChange}
                onBlur={handleBlur}
                className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
              />
              {errors.contact && (
                <p className="text-red-400 text-xs mt-1">
                  {errors.contact}
                </p>
              )}
            </div>

            {/* Password with Toggle */}
            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium mb-2"
              >
                Create password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  placeholder="8+ chars, uppercase, lowercase, number, special char"
                  value={formData.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  className="w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none rounded-xl border border-white/20"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword((v) => !v)}
                  className="absolute right-3 top-3 text-white"
                  aria-label="Toggle password visibility"
                >
                  {showPassword ? (
                    // Eye-off icon
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-5.523 0-10-4.03-10-7s4.477-7 10-7c2.35 0 4.507.838 6.125 2.225M3 3l18 18"
                      />
                    </svg>
                  ) : (
                    // Eye icon
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                      />
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-400 text-xs mt-1">
                  {errors.password}
                </p>
              )}
            </div>
          </div>

          {/* Terms */}
          <div className="flex items-start gap-2 mt-4">
            <input
              id="agree"
              name="agree"
              type="checkbox"
              checked={formData.agree}
              onChange={handleChange}
              className="mt-1 h-4 w-4"
            />
            <label htmlFor="agree" className="text-sm text-white/70">
              I agree to the{' '}
              <Link href="/terms" className="underline font-medium">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link
                href="/privacy-policy"
                className="underline font-medium"
              >
                Privacy Policy
              </Link>
            </label>
          </div>
          {errors.agree && (
            <p className="text-red-400 text-xs mt-1">
              {errors.agree}
            </p>
          )}

          <PrimaryButton
            type="submit"
            className="mt-4 w-full"
          >
            Continue to Step 2 →
          </PrimaryButton>
        </form>
      )}
    </div>
  );
}