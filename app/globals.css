@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom overrides */
body {
  background: #0d0d0d;
  color: #e5e7eb;
}

.badge {
  font-size: .65rem;
  background: #1f1f1f;
  padding: .25rem .75rem;
  border-radius: 9999px;
  letter-spacing: .04em;
  color: #a1a1aa;
}

.input-field {
  @apply bg-[#181818] border border-[#2d2d2d] text-[#e5e7eb] rounded-lg transition-all duration-200 ease-in-out;
}
.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59,130,246,0.1);
}

#waves-canvas,
.waves-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

#background-waves {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: -1;
}

.font-instrument-serif {
  font-family: 'Instrument Serif', serif !important;
}
.font-bricolage {
  font-family: 'Bricolage Grotesque', sans-serif !important;
}