// components/signup/background.tsx
import { ReactNode } from 'react';

type BackgroundProps = {
  children: ReactNode;
  imageSrc?: string;
};

export default function Background({
  children,
  imageSrc = 'https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=2160&q=80',
}: BackgroundProps) {
  return (
    <div
      className="relative min-h-screen bg-cover bg-center"
      style={{ backgroundImage: `url(${imageSrc})` }}
    >
      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/50" />

      {/* Foreground content */}
      <div className="relative z-10 flex items-center justify-center min-h-screen">
        {children}
      </div>
    </div>
  );
}