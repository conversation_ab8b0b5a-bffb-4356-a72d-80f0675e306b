'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSignUp } from '@clerk/nextjs';

export default function VerificationForm(): React.JSX.Element {
  const router = useRouter();
  const { isLoaded, signUp, setActive } = useSignUp();
  const [code, setCode] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!isLoaded) return;

    if (!/^\d{6}$/.test(code)) {
      setError('Enter a valid 6-digit code.');
      return;
    }

    setError('');
    setIsSubmitting(true);

    try {
      const verification = signUp.emailAddress ?
        await signUp.attemptEmailAddressVerification({ code }) :
        await signUp.attemptPhoneNumberVerification({ code });

      await setActive({ session: verification.createdSessionId });
      router.push('/personalize');
    } catch (err: any) {
      setError(err.errors?.[0]?.message || 'Invalid verification code');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-6 mt-6 mb-6">
      {/* Heading */}
      <div>
        <h2 className="text-2xl font-medium mb-2">Enter the code</h2>
        <p className="text-sm font-normal text-white/70">
          We’ve sent a 6-digit code to your email or phone. Please enter it below.
        </p>
      </div>

      {/* Input Field */}
      <div className="relative overflow-hidden rounded-xl">
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow:
              'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
            borderRadius: '12px',
          }}
        />
        <input
          type="text"
          inputMode="numeric"
          maxLength={6}
          value={code}
          onChange={(e) => setCode(e.target.value)}
          placeholder="123456"
          className="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
          required
        />
      </div>

      {/* Styled Error Message */}
      {error && (
        <p className="text-red-400 text-xs mt-1">
          {error}
        </p>
      )}

      {/* Verify Button */}
      <div className="relative overflow-hidden rounded-xl transition-custom hover:shadow-lg">
        <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
        <div className="absolute inset-0 z-10 bg-gradient-to-r from-white/30 to-white/20" />
        <div
          className="absolute inset-0 z-20"
          style={{
            boxShadow:
              'inset 2px 2px 1px 0 rgba(255,255,255,.5), inset -1px -1px 1px 1px rgba(255,255,255,.3)',
            borderRadius: '12px',
          }}
        />
        <button
          type="submit"
          className="z-30 relative w-full py-4 flex items-center justify-center gap-2 text-base font-semibold text-white bg-transparent"
        >
          Verify and Continue
          <svg
            width="16"
            height="16"
            fill="none"
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            viewBox="0 0 24 24"
          >
            <path d="M13 7l5 5-5 5M6 12h12" />
          </svg>
        </button>
      </div>

      {/* Resend Link */}
      <p className="text-sm text-white/60 text-center">
        Didn’t receive the code?{' '}
        <button
          type="button"
          onClick={() => alert('Resend OTP logic placeholder')}
          className="underline hover:opacity-80"
        >
          Resend
        </button>
      </p>
    </form>
  );
}